# AI预测系统 Docker Compose 配置
# 用于本地开发和测试环境

version: '3.8'

services:
  # PostgreSQL 主数据库
  postgres:
    image: postgres:15-alpine
    container_name: ai-prediction-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: ai_prediction_system
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ai-prediction-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存数据库
  redis:
    image: redis:7-alpine
    container_name: ai-prediction-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./infrastructure/docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ai-prediction-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MongoDB 文档数据库
  mongodb:
    image: mongo:7
    container_name: ai-prediction-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: ai_prediction_system
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./infrastructure/docker/mongodb/init.js:/docker-entrypoint-initdb.d/init.js
    networks:
      - ai-prediction-network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 10s
      timeout: 5s
      retries: 5

  # InfluxDB 时序数据库
  influxdb:
    image: influxdb:2.7-alpine
    container_name: ai-prediction-influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: password123
      DOCKER_INFLUXDB_INIT_ORG: ai-prediction-system
      DOCKER_INFLUXDB_INIT_BUCKET: metrics
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: ai-prediction-token-123456789
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - ai-prediction-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "influx", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: ai-prediction-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - ai-prediction-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: ai-prediction-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: password
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./infrastructure/docker/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    networks:
      - ai-prediction-network
    restart: unless-stopped
    healthcheck:
      test: rabbitmq-diagnostics -q ping
      interval: 30s
      timeout: 30s
      retries: 3

  # Elasticsearch 搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: ai-prediction-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - ai-prediction-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana 日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: ai-prediction-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - ai-prediction-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Prometheus 监控系统
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-prediction-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./infrastructure/monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ai-prediction-network
    restart: unless-stopped

  # Grafana 监控可视化
  grafana:
    image: grafana/grafana:latest
    container_name: ai-prediction-grafana
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./infrastructure/monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - ai-prediction-network
    restart: unless-stopped

  # MLflow 实验跟踪
  mlflow:
    image: python:3.8-slim
    container_name: ai-prediction-mlflow
    working_dir: /mlflow
    environment:
      MLFLOW_BACKEND_STORE_URI: ********************************************/ai_prediction_system
      MLFLOW_DEFAULT_ARTIFACT_ROOT: s3://mlflow-artifacts
      AWS_ACCESS_KEY_ID: minioadmin
      AWS_SECRET_ACCESS_KEY: minioadmin123
      MLFLOW_S3_ENDPOINT_URL: http://minio:9000
    ports:
      - "5000:5000"
    volumes:
      - ./infrastructure/docker/mlflow/requirements.txt:/mlflow/requirements.txt
    command: >
      bash -c "
        pip install -r requirements.txt &&
        mlflow server 
        --backend-store-uri ********************************************/ai_prediction_system
        --default-artifact-root s3://mlflow-artifacts
        --host 0.0.0.0
        --port 5000
      "
    depends_on:
      - postgres
      - minio
    networks:
      - ai-prediction-network
    restart: unless-stopped

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-prediction-backend
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=ai_prediction_system
      - REDIS_HOST=redis
      - MONGODB_HOST=mongodb
      - RABBITMQ_HOST=rabbitmq
      - MLFLOW_TRACKING_URI=http://mlflow:5000
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./data:/app/data
    depends_on:
      - postgres
      - redis
      - mongodb
      - rabbitmq
    networks:
      - ai-prediction-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery 异步任务处理
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-prediction-celery-worker
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=ai_prediction_system
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
    volumes:
      - ./backend:/app
      - ./data:/app/data
    command: celery -A app.core.celery worker --loglevel=info
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - ai-prediction-network
    restart: unless-stopped

  # Celery Beat 定时任务调度
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-prediction-celery-beat
    environment:
      - POSTGRES_SERVER=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=ai_prediction_system
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
    volumes:
      - ./backend:/app
      - ./data:/app/data
    command: celery -A app.core.celery beat --loglevel=info
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - ai-prediction-network
    restart: unless-stopped

# 数据卷定义
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  mongodb_data:
    driver: local
  influxdb_data:
    driver: local
  influxdb_config:
    driver: local
  minio_data:
    driver: local
  rabbitmq_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# 网络定义
networks:
  ai-prediction-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
