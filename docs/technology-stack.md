# AI预测系统技术选型方案

## 1. 技术选型概述

### 1.1 选型原则
- **成熟稳定**：选择经过生产环境验证的成熟技术
- **生态丰富**：优先选择生态系统完善的技术栈
- **性能优先**：满足高并发、低延迟的性能要求
- **可维护性**：便于团队开发和长期维护
- **扩展性**：支持系统的水平和垂直扩展
- **成本效益**：在满足需求的前提下控制技术成本

### 1.2 技术架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端技术栈                                  │
│  React 18 + TypeScript + Ant Design + Redux Toolkit        │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    API网关层                                  │
│         Kong + Nginx + JWT认证 + 限流熔断                     │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    后端服务层                                  │
│    Python 3.8+ + FastAPI + Pydantic + SQLAlchemy          │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    机器学习栈                                  │
│  scikit-learn + TensorFlow + PyTorch + MLflow + Optuna     │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                  │
│  PostgreSQL + Redis + MongoDB + InfluxDB + MinIO          │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    基础设施层                                  │
│     Docker + Kubernetes + Istio + Prometheus + ELK         │
└─────────────────────────────────────────────────────────────┘
```

## 2. 后端技术栈

### 2.1 编程语言：Python 3.8+
#### 2.1.1 选择理由
- **AI/ML生态丰富**：拥有最完善的机器学习和数据科学生态系统
- **开发效率高**：语法简洁，开发速度快，适合快速迭代
- **社区活跃**：庞大的开发者社区，丰富的第三方库
- **跨平台支持**：良好的跨平台兼容性

#### 2.1.2 核心优势
```python
# Python在AI/ML领域的优势示例
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from tensorflow import keras
import mlflow

# 数据处理简洁高效
data = pd.read_csv('data.csv')
features = data.select_dtypes(include=[np.number])

# 模型训练代码简洁
model = RandomForestClassifier(n_estimators=100)
model.fit(X_train, y_train)

# MLOps集成便捷
with mlflow.start_run():
    mlflow.sklearn.log_model(model, "model")
```

### 2.2 Web框架：FastAPI
#### 2.2.1 选择理由
- **高性能**：基于Starlette和Pydantic，性能接近Node.js和Go
- **现代化设计**：原生支持异步编程和类型提示
- **自动文档生成**：自动生成OpenAPI和JSON Schema文档
- **易于测试**：内置测试支持，便于单元测试和集成测试

#### 2.2.2 核心特性
```python
# FastAPI示例代码
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional

app = FastAPI(title="AI预测系统API", version="1.0.0")

class PredictionRequest(BaseModel):
    """预测请求模型"""
    model_id: str
    features: List[float]
    
class PredictionResponse(BaseModel):
    """预测响应模型"""
    prediction: float
    confidence: float
    model_version: str

@app.post("/predict", response_model=PredictionResponse)
async def predict(request: PredictionRequest):
    """实时预测接口"""
    # 自动类型验证和文档生成
    # 异步处理支持高并发
    pass
```

### 2.3 ORM框架：SQLAlchemy 2.0
#### 2.3.1 选择理由
- **功能强大**：支持复杂查询和关系映射
- **数据库无关**：支持多种数据库后端
- **异步支持**：SQLAlchemy 2.0原生支持异步操作
- **类型安全**：与Pydantic和mypy良好集成

#### 2.3.2 使用示例
```python
# SQLAlchemy 2.0异步模型定义
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import String, DateTime, Integer

class Base(DeclarativeBase):
    pass

class Model(Base):
    """模型表"""
    __tablename__ = "models"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    version: Mapped[str] = mapped_column(String(20), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

# 异步数据库操作
async def get_model(session: AsyncSession, model_id: int):
    """异步获取模型信息"""
    result = await session.get(Model, model_id)
    return result
```

## 3. 机器学习技术栈

### 3.1 经典机器学习：scikit-learn
#### 3.1.1 选择理由
- **算法全面**：涵盖分类、回归、聚类、降维等主要算法
- **API一致**：统一的API设计，易于学习和使用
- **文档完善**：详细的文档和丰富的示例
- **性能优化**：底层使用C/Cython实现，性能优秀

#### 3.1.2 应用场景
```python
# scikit-learn使用示例
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.model_selection import GridSearchCV, cross_val_score
from sklearn.metrics import classification_report, mean_squared_error
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler

# 构建机器学习流水线
pipeline = Pipeline([
    ('scaler', StandardScaler()),  # 数据标准化
    ('classifier', RandomForestClassifier())  # 分类器
])

# 超参数优化
param_grid = {
    'classifier__n_estimators': [100, 200, 300],
    'classifier__max_depth': [10, 20, None]
}

grid_search = GridSearchCV(pipeline, param_grid, cv=5, scoring='accuracy')
grid_search.fit(X_train, y_train)
```

### 3.2 深度学习：TensorFlow + PyTorch
#### 3.2.1 TensorFlow选择理由
- **生产就绪**：TensorFlow Serving提供完整的生产部署方案
- **生态完善**：TensorBoard、TensorFlow Extended (TFX)等工具链
- **移动端支持**：TensorFlow Lite支持移动端部署
- **分布式训练**：原生支持大规模分布式训练

#### 3.2.2 PyTorch选择理由
- **研究友好**：动态计算图，便于研究和实验
- **调试方便**：Python原生调试支持
- **社区活跃**：学术界广泛使用，最新算法实现丰富
- **部署改进**：TorchScript和TorchServe提升了部署能力

#### 3.2.3 使用策略
```python
# 双框架使用策略
# 研究和实验阶段使用PyTorch
import torch
import torch.nn as nn
import torch.optim as optim

class NeuralNetwork(nn.Module):
    """PyTorch神经网络模型"""
    def __init__(self, input_size, hidden_size, output_size):
        super(NeuralNetwork, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, output_size)
        self.relu = nn.ReLU()
    
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.fc2(x)
        return x

# 生产部署使用TensorFlow
import tensorflow as tf

def create_tensorflow_model(input_shape, num_classes):
    """TensorFlow生产模型"""
    model = tf.keras.Sequential([
        tf.keras.layers.Dense(128, activation='relu', input_shape=input_shape),
        tf.keras.layers.Dropout(0.2),
        tf.keras.layers.Dense(num_classes, activation='softmax')
    ])
    return model
```

### 3.3 MLOps平台：MLflow
#### 3.3.1 选择理由
- **实验跟踪**：完整的实验管理和版本控制
- **模型注册**：统一的模型注册中心
- **模型部署**：多种部署方式支持
- **框架无关**：支持各种机器学习框架

#### 3.3.2 核心功能
```python
# MLflow使用示例
import mlflow
import mlflow.sklearn
from mlflow.tracking import MlflowClient

# 实验跟踪
with mlflow.start_run(run_name="random_forest_experiment"):
    # 记录参数
    mlflow.log_param("n_estimators", 100)
    mlflow.log_param("max_depth", 10)
    
    # 训练模型
    model = RandomForestClassifier(n_estimators=100, max_depth=10)
    model.fit(X_train, y_train)
    
    # 记录指标
    accuracy = model.score(X_test, y_test)
    mlflow.log_metric("accuracy", accuracy)
    
    # 保存模型
    mlflow.sklearn.log_model(model, "model")

# 模型注册和版本管理
client = MlflowClient()
model_version = client.create_model_version(
    name="fraud_detection_model",
    source="runs:/run_id/model",
    run_id="run_id"
)
```

### 3.4 超参数优化：Optuna
#### 3.4.1 选择理由
- **高效优化**：基于贝叶斯优化的高效搜索算法
- **易于集成**：与主流机器学习框架无缝集成
- **可视化支持**：丰富的可视化工具
- **分布式支持**：支持分布式超参数优化

#### 3.4.2 使用示例
```python
# Optuna超参数优化示例
import optuna
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score

def objective(trial):
    """优化目标函数"""
    # 定义超参数搜索空间
    n_estimators = trial.suggest_int('n_estimators', 10, 1000)
    max_depth = trial.suggest_int('max_depth', 1, 32)
    min_samples_split = trial.suggest_float('min_samples_split', 0.1, 1.0)
    
    # 创建模型
    model = RandomForestClassifier(
        n_estimators=n_estimators,
        max_depth=max_depth,
        min_samples_split=min_samples_split,
        random_state=42
    )
    
    # 交叉验证评估
    scores = cross_val_score(model, X_train, y_train, cv=5)
    return scores.mean()

# 执行优化
study = optuna.create_study(direction='maximize')
study.optimize(objective, n_trials=100)

# 获取最优参数
best_params = study.best_params
print(f"最优参数: {best_params}")
```

## 4. 数据存储技术栈

### 4.1 关系型数据库：PostgreSQL 12+
#### 4.1.1 选择理由
- **ACID特性**：完整的事务支持，数据一致性保证
- **JSON支持**：原生JSON数据类型，支持NoSQL特性
- **扩展性强**：丰富的扩展插件，如PostGIS、TimescaleDB
- **性能优秀**：查询优化器先进，支持复杂查询

#### 4.1.2 配置优化
```sql
-- PostgreSQL性能优化配置
-- postgresql.conf关键参数
shared_buffers = '256MB'          -- 共享缓冲区
effective_cache_size = '1GB'      -- 有效缓存大小
work_mem = '4MB'                  -- 工作内存
maintenance_work_mem = '64MB'     -- 维护工作内存
checkpoint_completion_target = 0.9 -- 检查点完成目标
wal_buffers = '16MB'              -- WAL缓冲区
default_statistics_target = 100   -- 统计信息目标

-- 创建索引优化查询
CREATE INDEX CONCURRENTLY idx_models_created_at ON models(created_at);
CREATE INDEX CONCURRENTLY idx_predictions_model_id ON predictions(model_id);
```

### 4.2 缓存数据库：Redis 6.0+
#### 4.2.1 选择理由
- **高性能**：内存存储，微秒级响应时间
- **数据结构丰富**：支持字符串、哈希、列表、集合等多种数据结构
- **持久化支持**：RDB和AOF两种持久化方式
- **集群支持**：Redis Cluster提供水平扩展能力

#### 4.2.2 应用场景
```python
# Redis使用示例
import redis
import json
from typing import Optional

class RedisCache:
    """Redis缓存管理类"""
    
    def __init__(self, host='localhost', port=6379, db=0):
        self.redis_client = redis.Redis(
            host=host, 
            port=port, 
            db=db,
            decode_responses=True
        )
    
    async def cache_prediction_result(self, key: str, result: dict, ttl: int = 3600):
        """缓存预测结果"""
        await self.redis_client.setex(
            key, 
            ttl, 
            json.dumps(result)
        )
    
    async def get_cached_result(self, key: str) -> Optional[dict]:
        """获取缓存的预测结果"""
        cached_data = await self.redis_client.get(key)
        if cached_data:
            return json.loads(cached_data)
        return None
    
    async def cache_model_metadata(self, model_id: str, metadata: dict):
        """缓存模型元数据"""
        await self.redis_client.hset(
            f"model:{model_id}", 
            mapping=metadata
        )
```

### 4.3 文档数据库：MongoDB 4.4+
#### 4.3.1 选择理由
- **灵活的数据模型**：支持嵌套文档和数组，适合复杂数据结构
- **水平扩展**：原生支持分片，易于扩展
- **查询能力强**：丰富的查询操作符和聚合框架
- **高可用性**：副本集提供自动故障转移

#### 4.3.2 应用场景
```python
# MongoDB使用示例
from motor.motor_asyncio import AsyncIOMotorClient
from datetime import datetime
from typing import List, Dict, Any

class MongoDBManager:
    """MongoDB管理类"""
    
    def __init__(self, connection_string: str):
        self.client = AsyncIOMotorClient(connection_string)
        self.db = self.client.ai_prediction_system
    
    async def store_training_log(self, log_data: Dict[str, Any]):
        """存储训练日志"""
        log_data['timestamp'] = datetime.utcnow()
        await self.db.training_logs.insert_one(log_data)
    
    async def store_prediction_batch(self, batch_data: List[Dict[str, Any]]):
        """批量存储预测结果"""
        for item in batch_data:
            item['created_at'] = datetime.utcnow()
        await self.db.predictions.insert_many(batch_data)
    
    async def query_model_performance(self, model_id: str, days: int = 30):
        """查询模型性能数据"""
        pipeline = [
            {
                "$match": {
                    "model_id": model_id,
                    "timestamp": {
                        "$gte": datetime.utcnow() - timedelta(days=days)
                    }
                }
            },
            {
                "$group": {
                    "_id": {"$dateToString": {"format": "%Y-%m-%d", "date": "$timestamp"}},
                    "avg_accuracy": {"$avg": "$accuracy"},
                    "prediction_count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]
        
        cursor = self.db.model_metrics.aggregate(pipeline)
        return await cursor.to_list(length=None)
```

### 4.4 时序数据库：InfluxDB
#### 4.4.1 选择理由
- **时序优化**：专为时序数据设计，压缩率高
- **高写入性能**：支持高频率的数据写入
- **强大的查询语言**：Flux查询语言功能丰富
- **可视化集成**：与Grafana等可视化工具集成良好

#### 4.4.2 应用场景
```python
# InfluxDB使用示例
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS
import asyncio

class InfluxDBManager:
    """InfluxDB管理类"""
    
    def __init__(self, url: str, token: str, org: str, bucket: str):
        self.client = InfluxDBClient(url=url, token=token, org=org)
        self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
        self.query_api = self.client.query_api()
        self.bucket = bucket
        self.org = org
    
    def write_system_metrics(self, service_name: str, metrics: dict):
        """写入系统指标"""
        point = Point("system_metrics") \
            .tag("service", service_name) \
            .field("cpu_usage", metrics['cpu_usage']) \
            .field("memory_usage", metrics['memory_usage']) \
            .field("response_time", metrics['response_time'])
        
        self.write_api.write(bucket=self.bucket, org=self.org, record=point)
    
    def write_model_metrics(self, model_id: str, accuracy: float, latency: float):
        """写入模型性能指标"""
        point = Point("model_metrics") \
            .tag("model_id", model_id) \
            .field("accuracy", accuracy) \
            .field("latency", latency)
        
        self.write_api.write(bucket=self.bucket, org=self.org, record=point)
    
    def query_metrics(self, measurement: str, time_range: str = "-1h"):
        """查询指标数据"""
        query = f'''
        from(bucket: "{self.bucket}")
        |> range(start: {time_range})
        |> filter(fn: (r) => r._measurement == "{measurement}")
        '''
        
        result = self.query_api.query(org=self.org, query=query)
        return result
```

## 5. 前端技术栈

### 5.1 核心框架：React 18 + TypeScript
#### 5.1.1 选择理由
- **组件化开发**：可复用的组件架构，提高开发效率
- **类型安全**：TypeScript提供静态类型检查
- **生态丰富**：庞大的第三方库生态系统
- **性能优秀**：虚拟DOM和并发特性提供良好性能

#### 5.1.2 项目结构
```typescript
// 前端项目结构示例
src/
├── components/          // 通用组件
│   ├── common/         // 基础组件
│   ├── charts/         // 图表组件
│   └── forms/          // 表单组件
├── pages/              // 页面组件
│   ├── dashboard/      // 仪表板
│   ├── models/         // 模型管理
│   ├── predictions/    // 预测管理
│   └── users/          // 用户管理
├── services/           // API服务
├── store/              // 状态管理
├── utils/              // 工具函数
├── types/              // TypeScript类型定义
└── hooks/              // 自定义Hooks

// TypeScript类型定义示例
interface Model {
  id: string;
  name: string;
  version: string;
  algorithm: string;
  accuracy: number;
  status: 'training' | 'deployed' | 'archived';
  createdAt: string;
  updatedAt: string;
}

interface PredictionRequest {
  modelId: string;
  features: Record<string, any>;
  batchSize?: number;
}
```

### 5.2 UI框架：Ant Design Pro
#### 5.2.1 选择理由
- **企业级设计**：专为企业级应用设计的UI组件库
- **组件丰富**：提供完整的组件集合，覆盖常见业务场景
- **国际化支持**：内置国际化方案
- **主题定制**：支持主题定制和品牌化

#### 5.2.2 核心组件使用
```typescript
// Ant Design Pro组件使用示例
import React from 'react';
import { ProTable, ProColumns } from '@ant-design/pro-table';
import { Button, Tag, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

interface ModelTableProps {
  models: Model[];
  onEdit: (model: Model) => void;
  onDelete: (modelId: string) => void;
}

const ModelTable: React.FC<ModelTableProps> = ({ models, onEdit, onDelete }) => {
  const columns: ProColumns<Model>[] = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
    },
    {
      title: '算法类型',
      dataIndex: 'algorithm',
      key: 'algorithm',
      filters: true,
      valueEnum: {
        'random_forest': { text: '随机森林' },
        'neural_network': { text: '神经网络' },
        'svm': { text: '支持向量机' },
      },
    },
    {
      title: '准确率',
      dataIndex: 'accuracy',
      key: 'accuracy',
      render: (accuracy: number) => `${(accuracy * 100).toFixed(2)}%`,
      sorter: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          training: { color: 'processing', text: '训练中' },
          deployed: { color: 'success', text: '已部署' },
          archived: { color: 'default', text: '已归档' },
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => onEdit(record)}>
            编辑
          </Button>
          <Button type="link" danger onClick={() => onDelete(record.id)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <ProTable<Model>
      columns={columns}
      dataSource={models}
      rowKey="id"
      search={{
        labelWidth: 'auto',
      }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
      }}
      toolBarRender={() => [
        <Button key="create" type="primary" icon={<PlusOutlined />}>
          创建模型
        </Button>,
      ]}
    />
  );
};
```

### 5.3 状态管理：Redux Toolkit
#### 5.3.1 选择理由
- **简化Redux使用**：减少样板代码，提高开发效率
- **内置最佳实践**：集成了Redux的最佳实践
- **TypeScript支持**：完整的TypeScript类型支持
- **开发工具**：优秀的开发者工具支持

#### 5.3.2 状态管理示例
```typescript
// Redux Toolkit状态管理示例
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { modelService } from '../services/modelService';

// 异步操作
export const fetchModels = createAsyncThunk(
  'models/fetchModels',
  async (params: { page: number; pageSize: number }) => {
    const response = await modelService.getModels(params);
    return response.data;
  }
);

export const createModel = createAsyncThunk(
  'models/createModel',
  async (modelData: Partial<Model>) => {
    const response = await modelService.createModel(modelData);
    return response.data;
  }
);

// 状态切片
interface ModelsState {
  models: Model[];
  loading: boolean;
  error: string | null;
  total: number;
  currentPage: number;
}

const initialState: ModelsState = {
  models: [],
  loading: false,
  error: null,
  total: 0,
  currentPage: 1,
};

const modelsSlice = createSlice({
  name: 'models',
  initialState,
  reducers: {
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchModels.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchModels.fulfilled, (state, action) => {
        state.loading = false;
        state.models = action.payload.items;
        state.total = action.payload.total;
      })
      .addCase(fetchModels.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取模型列表失败';
      })
      .addCase(createModel.fulfilled, (state, action) => {
        state.models.unshift(action.payload);
        state.total += 1;
      });
  },
});

export const { setCurrentPage, clearError } = modelsSlice.actions;
export default modelsSlice.reducer;

// 选择器
export const selectModels = (state: RootState) => state.models.models;
export const selectModelsLoading = (state: RootState) => state.models.loading;
export const selectModelsError = (state: RootState) => state.models.error;
```

## 6. 基础设施技术栈

### 6.1 容器化：Docker
#### 6.1.1 选择理由
- **环境一致性**：开发、测试、生产环境完全一致
- **部署简化**：应用和依赖打包在一起，简化部署流程
- **资源隔离**：进程级别的资源隔离
- **版本管理**：镜像版本化管理，支持回滚

#### 6.1.2 Dockerfile示例
```dockerfile
# Python服务Dockerfile
FROM python:3.8-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 6.2 容器编排：Kubernetes
#### 6.2.1 选择理由
- **自动化部署**：声明式配置，自动化部署和管理
- **服务发现**：内置服务发现和负载均衡
- **自动扩展**：基于资源使用率的自动扩展
- **故障恢复**：自动故障检测和恢复

#### 6.2.2 Kubernetes配置示例
```yaml
# 部署配置 (deployment.yaml)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prediction-service
  labels:
    app: prediction-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: prediction-service
  template:
    metadata:
      labels:
        app: prediction-service
    spec:
      containers:
      - name: prediction-service
        image: ai-prediction-system/prediction-service:v1.0.0
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: redis-config
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
# 服务配置 (service.yaml)
apiVersion: v1
kind: Service
metadata:
  name: prediction-service
spec:
  selector:
    app: prediction-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: ClusterIP

---
# 水平自动扩展配置 (hpa.yaml)
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: prediction-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: prediction-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 6.3 监控系统：Prometheus + Grafana
#### 6.3.1 选择理由
- **时序数据库**：专为监控指标设计的时序数据库
- **强大的查询语言**：PromQL提供灵活的查询能力
- **告警系统**：完整的告警规则和通知机制
- **可视化**：Grafana提供丰富的可视化图表

#### 6.3.2 监控配置示例
```yaml
# Prometheus配置 (prometheus.yml)
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)

alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - alertmanager:9093

# 告警规则 (alert_rules.yml)
groups:
- name: ai-prediction-system
  rules:
  - alert: HighCPUUsage
    expr: cpu_usage_percent > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "CPU usage is above 80% for more than 5 minutes"
  
  - alert: ModelAccuracyDrop
    expr: model_accuracy < 0.8
    for: 10m
    labels:
      severity: critical
    annotations:
      summary: "Model accuracy dropped"
      description: "Model {{ $labels.model_id }} accuracy is below 80%"
  
  - alert: PredictionServiceDown
    expr: up{job="prediction-service"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Prediction service is down"
      description: "Prediction service has been down for more than 1 minute"
```

---

*本文档版本：v1.0*  
*最后更新时间：2025-08-26*  
*文档维护者：AI预测系统开发团队*
