# AI预测系统API接口规范

## 1. API概述

### 1.1 设计原则
- **RESTful设计**：遵循REST架构风格，使用标准HTTP方法
- **统一响应格式**：所有API采用统一的响应数据格式
- **版本控制**：通过URL路径进行API版本管理
- **安全认证**：基于JWT的身份认证和权限控制
- **错误处理**：标准化的错误码和错误信息
- **文档自动生成**：基于OpenAPI 3.0规范自动生成文档

### 1.2 基础信息
- **Base URL**: `https://api.ai-prediction.com/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: UTF-8

### 1.3 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-08-26T10:30:00Z",
  "request_id": "req_123456789"
}
```

### 1.4 错误响应格式
```json
{
  "code": 400,
  "message": "参数验证失败",
  "error": {
    "type": "ValidationError",
    "details": [
      {
        "field": "model_id",
        "message": "模型ID不能为空"
      }
    ]
  },
  "timestamp": "2025-08-26T10:30:00Z",
  "request_id": "req_123456789"
}
```

### 1.5 状态码说明
| 状态码 | 说明 | 描述 |
|--------|------|------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未授权访问 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突 |
| 422 | Unprocessable Entity | 请求格式正确但语义错误 |
| 429 | Too Many Requests | 请求频率超限 |
| 500 | Internal Server Error | 服务器内部错误 |

## 2. 认证授权API

### 2.1 用户登录
```http
POST /auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123",
  "remember_me": true
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user": {
      "id": "user_123",
      "username": "<EMAIL>",
      "name": "管理员",
      "role": "admin",
      "tenant_id": "tenant_001"
    }
  }
}
```

### 2.2 刷新令牌
```http
POST /auth/refresh
Content-Type: application/json
Authorization: Bearer <refresh_token>

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 2.3 用户登出
```http
POST /auth/logout
Authorization: Bearer <access_token>
```

### 2.4 获取用户信息
```http
GET /auth/me
Authorization: Bearer <access_token>
```

## 3. 数据管理API

### 3.1 数据集管理

#### 3.1.1 创建数据集
```http
POST /datasets
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "客户流失预测数据集",
  "description": "用于预测客户流失的历史数据",
  "source_type": "file_upload",
  "file_format": "csv",
  "schema": {
    "columns": [
      {
        "name": "customer_id",
        "type": "string",
        "description": "客户ID"
      },
      {
        "name": "age",
        "type": "integer",
        "description": "客户年龄"
      },
      {
        "name": "churn",
        "type": "boolean",
        "description": "是否流失"
      }
    ]
  },
  "tags": ["客户分析", "流失预测"]
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "数据集创建成功",
  "data": {
    "id": "dataset_123",
    "name": "客户流失预测数据集",
    "description": "用于预测客户流失的历史数据",
    "status": "created",
    "row_count": 0,
    "column_count": 3,
    "file_size": 0,
    "created_at": "2025-08-26T10:30:00Z",
    "updated_at": "2025-08-26T10:30:00Z",
    "created_by": "user_123"
  }
}
```

#### 3.1.2 上传数据文件
```http
POST /datasets/{dataset_id}/upload
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

file: <binary_data>
```

#### 3.1.3 获取数据集列表
```http
GET /datasets?page=1&page_size=20&status=active&tag=客户分析
Authorization: Bearer <access_token>
```

#### 3.1.4 获取数据集详情
```http
GET /datasets/{dataset_id}
Authorization: Bearer <access_token>
```

#### 3.1.5 数据预览
```http
GET /datasets/{dataset_id}/preview?limit=100
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "columns": ["customer_id", "age", "income", "churn"],
    "rows": [
      ["cust_001", 25, 50000, false],
      ["cust_002", 35, 75000, true],
      ["cust_003", 45, 60000, false]
    ],
    "total_rows": 10000,
    "preview_rows": 3
  }
}
```

### 3.2 数据预处理

#### 3.2.1 创建预处理任务
```http
POST /datasets/{dataset_id}/preprocessing
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "数据清洗和特征工程",
  "steps": [
    {
      "type": "remove_duplicates",
      "config": {}
    },
    {
      "type": "handle_missing_values",
      "config": {
        "strategy": "mean",
        "columns": ["age", "income"]
      }
    },
    {
      "type": "feature_scaling",
      "config": {
        "method": "standard_scaler",
        "columns": ["age", "income"]
      }
    },
    {
      "type": "encode_categorical",
      "config": {
        "method": "one_hot",
        "columns": ["gender", "region"]
      }
    }
  ]
}
```

#### 3.2.2 获取预处理任务状态
```http
GET /datasets/{dataset_id}/preprocessing/{task_id}
Authorization: Bearer <access_token>
```

#### 3.2.3 获取数据质量报告
```http
GET /datasets/{dataset_id}/quality_report
Authorization: Bearer <access_token>
```

## 4. 模型管理API

### 4.1 模型训练

#### 4.1.1 创建训练任务
```http
POST /models/train
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "客户流失预测模型v1.0",
  "dataset_id": "dataset_123",
  "algorithm": "random_forest",
  "target_column": "churn",
  "feature_columns": ["age", "income", "tenure", "monthly_charges"],
  "hyperparameters": {
    "n_estimators": 100,
    "max_depth": 10,
    "min_samples_split": 2,
    "random_state": 42
  },
  "validation_config": {
    "method": "cross_validation",
    "folds": 5,
    "test_size": 0.2
  },
  "auto_tune": true,
  "tune_config": {
    "n_trials": 50,
    "timeout": 3600
  }
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "训练任务创建成功",
  "data": {
    "task_id": "train_task_456",
    "model_id": "model_789",
    "status": "queued",
    "estimated_duration": 1800,
    "created_at": "2025-08-26T10:30:00Z"
  }
}
```

#### 4.1.2 获取训练任务状态
```http
GET /models/train/{task_id}
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "task_id": "train_task_456",
    "model_id": "model_789",
    "status": "training",
    "progress": 65,
    "current_step": "hyperparameter_tuning",
    "metrics": {
      "current_accuracy": 0.85,
      "best_accuracy": 0.87,
      "current_trial": 32,
      "total_trials": 50
    },
    "logs": [
      {
        "timestamp": "2025-08-26T10:35:00Z",
        "level": "INFO",
        "message": "开始超参数优化"
      },
      {
        "timestamp": "2025-08-26T10:40:00Z",
        "level": "INFO",
        "message": "完成第32次试验，当前最佳准确率: 0.87"
      }
    ],
    "estimated_remaining": 900
  }
}
```

#### 4.1.3 停止训练任务
```http
POST /models/train/{task_id}/stop
Authorization: Bearer <access_token>
```

### 4.2 模型管理

#### 4.2.1 获取模型列表
```http
GET /models?page=1&page_size=20&status=deployed&algorithm=random_forest
Authorization: Bearer <access_token>
```

#### 4.2.2 获取模型详情
```http
GET /models/{model_id}
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "model_789",
    "name": "客户流失预测模型v1.0",
    "version": "1.0.0",
    "algorithm": "random_forest",
    "status": "deployed",
    "accuracy": 0.87,
    "precision": 0.85,
    "recall": 0.89,
    "f1_score": 0.87,
    "auc": 0.92,
    "feature_importance": [
      {
        "feature": "monthly_charges",
        "importance": 0.35
      },
      {
        "feature": "tenure",
        "importance": 0.28
      },
      {
        "feature": "age",
        "importance": 0.22
      },
      {
        "feature": "income",
        "importance": 0.15
      }
    ],
    "hyperparameters": {
      "n_estimators": 150,
      "max_depth": 12,
      "min_samples_split": 3
    },
    "dataset_id": "dataset_123",
    "training_duration": 1650,
    "model_size": "15.2MB",
    "created_at": "2025-08-26T10:30:00Z",
    "deployed_at": "2025-08-26T12:15:00Z",
    "created_by": "user_123"
  }
}
```

#### 4.2.3 模型评估
```http
POST /models/{model_id}/evaluate
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "test_dataset_id": "dataset_456",
  "metrics": ["accuracy", "precision", "recall", "f1_score", "auc"]
}
```

#### 4.2.4 模型部署
```http
POST /models/{model_id}/deploy
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "deployment_name": "客户流失预测服务",
  "environment": "production",
  "instance_count": 3,
  "cpu_limit": "1000m",
  "memory_limit": "2Gi",
  "auto_scaling": {
    "enabled": true,
    "min_replicas": 2,
    "max_replicas": 10,
    "target_cpu_utilization": 70
  }
}
```

#### 4.2.5 模型版本管理
```http
GET /models/{model_id}/versions
Authorization: Bearer <access_token>
```

## 5. 预测服务API

### 5.1 实时预测

#### 5.1.1 单条预测
```http
POST /predictions/realtime
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "model_id": "model_789",
  "features": {
    "age": 35,
    "income": 75000,
    "tenure": 24,
    "monthly_charges": 89.99
  },
  "return_confidence": true,
  "return_explanation": true
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "预测成功",
  "data": {
    "prediction": 1,
    "prediction_label": "流失",
    "confidence": 0.85,
    "probabilities": {
      "不流失": 0.15,
      "流失": 0.85
    },
    "explanation": {
      "feature_contributions": [
        {
          "feature": "monthly_charges",
          "value": 89.99,
          "contribution": 0.35
        },
        {
          "feature": "tenure",
          "value": 24,
          "contribution": -0.12
        }
      ]
    },
    "model_version": "1.0.0",
    "prediction_time": "2025-08-26T10:30:00Z",
    "prediction_id": "pred_123456"
  }
}
```

#### 5.1.2 批量预测
```http
POST /predictions/batch
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "model_id": "model_789",
  "features": [
    {
      "customer_id": "cust_001",
      "age": 35,
      "income": 75000,
      "tenure": 24,
      "monthly_charges": 89.99
    },
    {
      "customer_id": "cust_002",
      "age": 45,
      "income": 60000,
      "tenure": 36,
      "monthly_charges": 65.50
    }
  ],
  "return_confidence": true
}
```

### 5.2 批量预测任务

#### 5.2.1 创建批量预测任务
```http
POST /predictions/batch_task
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "月度客户流失预测",
  "model_id": "model_789",
  "input_dataset_id": "dataset_999",
  "output_format": "csv",
  "notification_config": {
    "email": "<EMAIL>",
    "webhook": "https://example.com/webhook"
  }
}
```

#### 5.2.2 获取批量预测任务状态
```http
GET /predictions/batch_task/{task_id}
Authorization: Bearer <access_token>
```

#### 5.2.3 下载预测结果
```http
GET /predictions/batch_task/{task_id}/download
Authorization: Bearer <access_token>
```

### 5.3 预测历史

#### 5.3.1 获取预测历史
```http
GET /predictions/history?model_id=model_789&start_date=2025-08-01&end_date=2025-08-26&page=1&page_size=50
Authorization: Bearer <access_token>
```

#### 5.3.2 获取预测统计
```http
GET /predictions/statistics?model_id=model_789&period=7d
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_predictions": 15420,
    "daily_predictions": [
      {
        "date": "2025-08-20",
        "count": 2150,
        "avg_confidence": 0.82
      },
      {
        "date": "2025-08-21",
        "count": 2380,
        "avg_confidence": 0.84
      }
    ],
    "prediction_distribution": {
      "不流失": 12336,
      "流失": 3084
    },
    "confidence_distribution": {
      "0.0-0.5": 1542,
      "0.5-0.7": 3084,
      "0.7-0.9": 7710,
      "0.9-1.0": 3084
    }
  }
}
```

## 6. 监控管理API

### 6.1 系统监控

#### 6.1.1 获取系统状态
```http
GET /monitoring/system/status
Authorization: Bearer <access_token>
```

#### 6.1.2 获取系统指标
```http
GET /monitoring/system/metrics?start_time=2025-08-26T00:00:00Z&end_time=2025-08-26T23:59:59Z&interval=1h
Authorization: Bearer <access_token>
```

### 6.2 模型监控

#### 6.2.1 获取模型性能指标
```http
GET /monitoring/models/{model_id}/performance?period=7d
Authorization: Bearer <access_token>
```

#### 6.2.2 数据漂移检测
```http
POST /monitoring/models/{model_id}/drift_detection
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "reference_dataset_id": "dataset_123",
  "current_dataset_id": "dataset_456",
  "features": ["age", "income", "tenure"],
  "threshold": 0.05
}
```

### 6.3 告警管理

#### 6.3.1 获取告警列表
```http
GET /monitoring/alerts?status=active&severity=critical&page=1&page_size=20
Authorization: Bearer <access_token>
```

#### 6.3.2 创建告警规则
```http
POST /monitoring/alert_rules
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "模型准确率下降告警",
  "description": "当模型准确率低于80%时触发告警",
  "condition": {
    "metric": "model_accuracy",
    "operator": "less_than",
    "threshold": 0.8,
    "duration": "5m"
  },
  "severity": "critical",
  "notification_channels": ["email", "webhook"]
}
```

## 7. 用户管理API

### 7.1 用户管理

#### 7.1.1 创建用户
```http
POST /users
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "username": "<EMAIL>",
  "name": "数据科学家",
  "email": "<EMAIL>",
  "role": "data_scientist",
  "tenant_id": "tenant_001",
  "password": "temp_password_123"
}
```

#### 7.1.2 获取用户列表
```http
GET /users?page=1&page_size=20&role=data_scientist&status=active
Authorization: Bearer <access_token>
```

#### 7.1.3 更新用户信息
```http
PUT /users/{user_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "高级数据科学家",
  "role": "senior_data_scientist",
  "status": "active"
}
```

### 7.2 权限管理

#### 7.2.1 获取用户权限
```http
GET /users/{user_id}/permissions
Authorization: Bearer <access_token>
```

#### 7.2.2 分配权限
```http
POST /users/{user_id}/permissions
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "permissions": [
    "dataset:read",
    "dataset:write",
    "model:read",
    "model:train",
    "prediction:read"
  ]
}
```

## 8. 数据模型定义

### 8.1 用户模型
```json
{
  "id": "string",
  "username": "string",
  "name": "string",
  "email": "string",
  "role": "admin|data_scientist|business_user",
  "status": "active|inactive|suspended",
  "tenant_id": "string",
  "created_at": "datetime",
  "updated_at": "datetime",
  "last_login": "datetime"
}
```

### 8.2 数据集模型
```json
{
  "id": "string",
  "name": "string",
  "description": "string",
  "source_type": "file_upload|database|api",
  "file_format": "csv|json|parquet",
  "status": "created|uploading|processing|ready|error",
  "row_count": "integer",
  "column_count": "integer",
  "file_size": "integer",
  "schema": "object",
  "tags": ["string"],
  "created_at": "datetime",
  "updated_at": "datetime",
  "created_by": "string"
}
```

### 8.3 模型模型
```json
{
  "id": "string",
  "name": "string",
  "version": "string",
  "algorithm": "string",
  "status": "training|trained|deployed|archived|error",
  "accuracy": "float",
  "precision": "float",
  "recall": "float",
  "f1_score": "float",
  "auc": "float",
  "hyperparameters": "object",
  "feature_importance": "array",
  "dataset_id": "string",
  "model_size": "string",
  "training_duration": "integer",
  "created_at": "datetime",
  "deployed_at": "datetime",
  "created_by": "string"
}
```

### 8.4 预测模型
```json
{
  "id": "string",
  "model_id": "string",
  "model_version": "string",
  "input_features": "object",
  "prediction": "any",
  "confidence": "float",
  "probabilities": "object",
  "prediction_time": "datetime",
  "response_time": "integer",
  "user_id": "string"
}
```

---

*本文档版本：v1.0*  
*最后更新时间：2025-08-26*  
*文档维护者：AI预测系统开发团队*
