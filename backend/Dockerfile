# AI预测系统后端服务 Dockerfile
# 基于Python 3.8的多阶段构建

# 构建阶段
FROM python:3.8-slim as builder

# 设置构建参数
ARG BUILD_DATE
ARG VERSION
ARG VCS_REF

# 添加标签信息
LABEL maintainer="AI预测系统开发团队" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="ai-prediction-system-backend" \
      org.label-schema.description="AI预测系统后端API服务" \
      org.label-schema.version=$VERSION \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.vcs-url="https://git.atjog.com/aier/ai-prediction-system" \
      org.label-schema.schema-version="1.0"

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    POETRY_VERSION=1.6.1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    gcc \
    g++ \
    git \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry==$POETRY_VERSION

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt pyproject.toml* poetry.lock* ./

# 安装Python依赖
RUN if [ -f pyproject.toml ]; then \
        poetry config virtualenvs.create false && \
        poetry install --no-dev --no-interaction --no-ansi; \
    else \
        pip install --no-cache-dir -r requirements.txt; \
    fi

# 生产阶段
FROM python:3.8-slim as production

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/app/.local/bin:$PATH" \
    PYTHONPATH="/app:$PYTHONPATH"

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    libpq5 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 从构建阶段复制Python包
COPY --from=builder /usr/local/lib/python3.8/site-packages /usr/local/lib/python3.8/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/data/models /app/data/datasets /app/logs \
    && chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
