# Web框架和API
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库和ORM
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.1
redis==5.0.1
motor==3.3.2
pymongo==4.6.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 机器学习和数据科学
scikit-learn==1.3.2
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4
joblib==1.3.2

# 深度学习框架
tensorflow==2.15.0
torch==2.1.2
torchvision==0.16.2

# MLOps和实验管理
mlflow==2.8.1
optuna==3.4.0
shap==0.43.0

# 数据处理和可视化
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# 异步和并发
celery==5.3.4
aioredis==2.0.1
httpx==0.25.2

# 监控和日志
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk==1.38.0

# 文件处理
openpyxl==3.1.2
xlrd==2.0.1
python-magic==0.4.27

# 配置和环境
python-dotenv==1.0.0
pyyaml==6.0.1
click==8.1.7

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
faker==20.1.0

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# 部署和容器
gunicorn==21.2.0
docker==6.1.3
kubernetes==28.1.0

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 加密和哈希
cryptography==41.0.8
bcrypt==4.1.2

# 数据验证
cerberus==1.3.5
marshmallow==3.20.1

# 缓存
cachetools==5.3.2
diskcache==5.6.3

# 网络和HTTP
requests==2.31.0
aiohttp==3.9.1
websockets==12.0

# 数据库迁移和管理
psycopg2-binary==2.9.9
pymysql==1.1.0

# 消息队列
kombu==5.3.4
billiard==4.2.0

# 工具库
more-itertools==10.1.0
toolz==0.12.0
attrs==23.1.0
